extends Control

# Jed<PERSON><PERSON><PERSON>ý a funkčný ChaptersMenu script

@onready var title_label: Label = $ContentContainer/TitleLabel
@onready var current_chapter_label: Label = $ContentContainer/MainContainer/ChapterInfo/CurrentChapterLabel
@onready var chapter_title_label: Label = $ContentContainer/MainContainer/ChapterInfo/ChapterTitleLabel
@onready var chapter_description: RichTextLabel = $ContentContainer/MainContainer/ChapterInfo/ChapterDescription
@onready var chapter_image: TextureRect = $ContentContainer/MainContainer/ChapterImageContainer/ChapterImageFrame/ChapterImage
@onready var prev_button: Button = $ContentContainer/NavigationContainer/PrevButton
@onready var next_button: Button = $ContentContainer/NavigationContainer/NextButton
@onready var play_button: Button = $ContentContainer/ButtonsContainer/PlayButton
@onready var back_button: Button = $ContentContainer/ButtonsContainer/BackButton

var current_chapter: int = 1
var max_chapters: int = 7

# <PERSON><PERSON>r<PERSON>zky kapitol
var chapter_images = {
	1: preload("res://assets/Kapitoly_VISUALS/Kapitola_1.png"),
	2: preload("res://assets/Kapitoly_VISUALS/Kapitola_2.png"),
	3: preload("res://assets/Kapitoly_VISUALS/Kapitola_3.png"),
	4: preload("res://assets/Kapitoly_VISUALS/Kapitola_4.png"),
	5: preload("res://assets/Kapitoly_VISUALS/Kapitola_5.png"),
	6: preload("res://assets/Kapitoly_VISUALS/Kapitola_6.png"),
	7: preload("res://assets/Kapitoly_VISUALS/Kapitola_7.png")
}

# Informácie o kapitolách
var chapter_info = {
	1: {
		"title": "Záhadný začiatok",
		"description": "Marec 1894. Búrlivá cesta cez karpatské horstvo k zámku Van Helsinga. Váš mentor potrebuje pomoc s nebezpečnou misiou."
	},
	2: {
		"title": "Hlbšie do temnoty",
		"description": "Stojíte pred masívnou železnou bránou zdobenou heraldickými symbolmi. Brána je zamknutá a na nej je krvou napísaný text."
	},
	3: {
		"title": "Pátranie v zámku",
		"description": "Vstupujete do veľkej haly osvetlenej len plamenným mihotaním krbu. Viktor vám rozpráva o zmiznutí Van Helsinga."
	},
	4: {
		"title": "Tajné krídlo",
		"description": "Objavujete laboratórium plné alchymistických nástrojov a záhadných experimentov. Stopy vedú do hlbších častí zámku."
	},
	5: {
		"title": "Krypty",
		"description": "Schádzate do pradávnych katakomby pod zámkom. Tieto miesta neboli navštívené celé storočia."
	},
	6: {
		"title": "Konfrontácia",
		"description": "Konečné stretnutie s grófkou Isabelle Báthoryovou. Osud Van Helsinga a celého sveta visí na vlásku."
	},
	7: {
		"title": "Záchrana mentora",
		"description": "Epilóg príbehu. Posledné momenty v zámku a záchrana vašeho mentora pred večnou temnotou."
	}
}

func _ready():
	print("ChaptersMenu načítané")
	
	# Pripojenie signálov
	connect_signals()
	
	# Aplikovanie fontov
	apply_fonts()
	
	# Aktualizovanie zobrazenia
	update_chapter_display()
	
	# Nastavenie fokusu
	if play_button:
		play_button.grab_focus()

func connect_signals():
	"""Pripojenie signálov"""
	if prev_button:
		prev_button.pressed.connect(_on_prev_pressed)
	if next_button:
		next_button.pressed.connect(_on_next_pressed)
	if play_button:
		play_button.pressed.connect(_on_play_pressed)
	if back_button:
		back_button.pressed.connect(_on_back_pressed)

func apply_fonts():
	"""Aplikuje fonty na UI elementy"""
	if title_label:
		FontLoader.apply_font_style(title_label, "chapter_title")
		title_label.add_theme_color_override("font_color", Color("#D4AF37"))
		title_label.add_theme_font_size_override("font_size", 36)

	if current_chapter_label:
		FontLoader.apply_font_style(current_chapter_label, "chapter_title")
		current_chapter_label.add_theme_color_override("font_color", Color("#D4AF37"))
		current_chapter_label.add_theme_font_size_override("font_size", 32)
	
	if chapter_title_label:
		FontLoader.apply_font_style(chapter_title_label, "character_dialogue")
		chapter_title_label.add_theme_color_override("font_color", Color("#F5F5DC"))
		chapter_title_label.add_theme_font_size_override("font_size", 24)
	
	if chapter_description:
		FontLoader.apply_font_style(chapter_description, "character_dialogue")
		chapter_description.add_theme_color_override("default_color", Color("#E0E0E0"))
		chapter_description.add_theme_font_size_override("normal_font_size", 18)
	
	# Tlačidlá - už používajú tému automaticky

func update_chapter_display():
	"""Aktualizuje zobrazenie aktuálnej kapitoly"""
	if current_chapter_label:
		current_chapter_label.text = "KAPITOLA " + str(current_chapter)

	if chapter_title_label and chapter_info.has(current_chapter):
		chapter_title_label.text = chapter_info[current_chapter]["title"]

	if chapter_description and chapter_info.has(current_chapter):
		chapter_description.text = chapter_info[current_chapter]["description"]

	# Aktualizovanie obrázka kapitoly
	if chapter_image and chapter_images.has(current_chapter):
		chapter_image.texture = chapter_images[current_chapter]

	# Aktualizovanie tlačidiel
	if prev_button:
		prev_button.disabled = (current_chapter <= 1)
	if next_button:
		next_button.disabled = (current_chapter >= max_chapters)

func _on_prev_pressed():
	if current_chapter > 1:
		current_chapter -= 1
		update_chapter_display()
		AudioManager.play_menu_button_sound()

func _on_next_pressed():
	if current_chapter < max_chapters:
		current_chapter += 1
		update_chapter_display()
		AudioManager.play_menu_button_sound()

func _on_play_pressed():
	print("Spúšťam kapitolu ", current_chapter)
	AudioManager.play_menu_button_sound()
	GameManager.go_to_chapter(current_chapter)

func _on_back_pressed():
	print("Návrat do hlavného menu")
	AudioManager.play_menu_button_sound()
	GameManager.go_to_main_menu()

func _input(event):
	if event.is_action_pressed("ui_cancel"):
		_on_back_pressed()
	elif event.is_action_pressed("ui_left"):
		_on_prev_pressed()
	elif event.is_action_pressed("ui_right"):
		_on_next_pressed()
	elif event.is_action_pressed("ui_accept"):
		_on_play_pressed()
